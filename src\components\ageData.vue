<template>
  <div ref="EcharRef1" id="echarts1"></div>
</template>

<script setup>
import * as echarts from "echarts";
import { onMounted, getCurrentInstance, reactive, ref } from 'vue';
// 声明组件要触发的事件
const emits = defineEmits(['increase']);
// 声明接收参数
const props = defineProps({
  dataArr: Array
});
// //  点击事件
// const handelClick = () => {
//   console.log('触发子组件点击')
//   // 触发父组件事件
//   emits('increase')
// };
onMounted(() => {
  // 获取页面的实例对象
  const pageInstance = getCurrentInstance();
  // 获取dom节点对象
  const tagDomObj = pageInstance.refs.EcharRef1;
  const myEcharts = echarts.init(tagDomObj);
  const data = reactive({
    data: [{
      name: 'user1',
      value: 10,
      sum: 10
    },
    {
      name: 'user2',
      value: 20,
      sum: 10
    },
    {
      name: 'user3',
      value: 23,
      sum: 50
    },
    {
      name: 'user4',
      value: 44,
      sum: 60
    }
    ]
  }
  )
  const total = ref(0)
  total.value = data.data.reduce((pre, v) => {
    return pre += v.value
  }, 0)
  console.log(total.value);

  function getArrByKey(data, k) {
    let key = k || "value";
    let res = [];
    if (data) {
      data.forEach(function (t) {
        res.push(t[key]);
      });
    }
    return res;
  };
  function getSymbolData(data) {
    let arr = [];
    for (var i = 0; i < data.length; i++) {
      arr.push({
        value: data[i].value,
        symbolPosition: 'end'
      })
    }
    return arr;
  }
  var option = {
    backgroundColor: 'transparent',
    grid: {
      top: '2%',
      bottom: -15,
      right: 30,
      left: 30,
      containLabel: true
    },
    xAxis: {
      show: false
    },
    yAxis: [{
      triggerEvent: true,
      show: true,
      inverse: true,
      data: getArrByKey(data.data, 'name'),
      axisLine: {
        show: false
      },
      splitLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        show: false,
        interval: 0,
        color: 'red',
        align: 'left',
        margin: 80,
        fontSize: 13,
        formatter: function (value, index) {
          return '{title|' + value + '}'
        },
        rich: {
          title: {
            width: 165
          }
        }
      },
    }, {
      triggerEvent: true,
      show: true,
      inverse: true,
      data: getArrByKey(data.data, 'name'),
      axisLine: {
        show: false
      },
      splitLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      // 右侧的数值
      axisLabel: {
        interval: 0,
        shadowOffsetX: '-20px',
        color: '#04BAFF',
        align: 'right',
        verticalAlign: 'bottom',
        lineHeight: 30,
        fontSize: 20,
        fontStyle: 'italic',
        formatter: function (value, index) {
          return ((data.data[index].value / total.value).toFixed(1)) * 100 + '%'
        },
      }
    }],
    series: [{
      name: 'XXX',
      type: 'pictorialBar',
      symbol: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADoAAAA6CAMAAADWZboaAAAAZlBMVEUAAABe3uVe3+Vf3uVf3+Zf3uVg3+Zg3+Zf3+Vi4OZh4OZg3+Z86/Bh3+Zi4Odj4Odi4OZ86/B76/B86/Bj4ed56+9x5+xn4umB7/N87PB36e+A7/N+7fF/7vJ/7vJ+7fGA7/OB7/PReX+lAAAAIXRSTlMABQkVDREmIhk3MR10LEFFPHh7cUprXE35h2XnqMLAp+mHAG9cAAAB5ElEQVRIx83WjU7CMBQFYIoiKMqU/XUboHv/l/Tce7t2XamDNSacETEmX86tlK2rx4py150o+MstMBLwWRfHKo6JCVxLnvmFGBjFQ58oF1//sUZhGy/ClSTWObgnL4O+bkeN4nY2okfNMbkRt9/vtxz8InoTsWplJSCzFxPmO8+GpSIByX3YQAuGDWtRKhKjCnxDXhF6Z4yxnZ20Wgko7BMRDmxtSGVaI4kdTIgb+zTYoJQlIMlDlmUFgrcDWWC201qSayqlTkiCddWWeV62VU0YlnpRi9VOKaSUsiyq/N0krwq2Ugt7lVpZl5BfHNiytjagMi+XYp0kCR45hMlivVQrE/uU5pXSrCB5bM6d1t2lOZItMqmliT3q5uVxqxzyW/ccfYLNKx7ZTeykMvNyac2yt2Fbc61MHLSC0rwoxbiNdlQ3GBm1NLHQsHUrtEXppR/ljNpW6DbSCoqlFiVoN6YdaFlgsSFVPs1BdT8OaB5QyQzVcaqWDows/zepxR8ObLglTrdtCRVuRNj4Rrxh+//0ke2f8KVL+Kon3GCSbmsJN9OUW3j6g0Ns+LgCij2u0h+Sghc8mlMPBMgdx5DFh59VmOVHrvmDnoNxCz3J7MFWsMuaLyR089xz/xhlfijvwutR8gv3zk6BLUUeCgAAAABJRU5ErkJggg==',
      symbolSize: [60, 60],
      symbolOffset: [20, 0],
      z: 12,
      itemStyle: {
        color: '#fff'
      },
      data: getSymbolData(data.data)
    }, {
      name: '条',
      type: 'bar',
      showBackground: true,
      backgroundStyle: {
        borderRadius: 30
      },
      barBorderRadius: 30,
      yAxisIndex: 0,
      data: data.data,
      barWidth: 18,
      // align: left,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(
          0,
          0,
          1,
          0,
          [{
            offset: 0,
            color: '#1488dc'
          },
          // {
          //   offset: 0.7,
          //   color: '#A71A2B'
          // },
          {
            offset: 1,
            color: '#21eafc'
          }
          ],
          false
        ),
        barBorderRadius: 10,
        // color: '#A71A2B',
        barBorderRadius: 4,
      },
      label: {
        show: true,
        color: '#fff',
        position: [0, '-20px'],
        textStyle: {
          fontSize: 16
        },
        formatter: function (a, b) {
          return ` ${a.name}  {hou|${a.value}} 人`
          // return `<span>${a.name}</span>  <span style="fontSize:18px;fontWeight:bold">${a.value} 人</span>`
        },
        rich: {
          hou: {
            fontWeight: 'bold',
            fontSize: 20
          }
        }
      }
    }]
  };
  option && myEcharts.setOption(option);
})


</script>

<style lang="scss" scoped>
#echarts1 {
  width: 500px;
  height: 300px;
  margin: auto;
  background-color: transparent;
}
</style>