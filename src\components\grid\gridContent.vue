<template>
  <div class="_box">
    <div class="single" @click="openBox(1)">
      <div class="top">{{ 1 }}</div>
      <img src="/di02.png" alt="" />
      <span class="bottom">党支部（个）</span>
    </div>
    <div class="single" @click="openBox(2)">
      <div class="top">{{ props.xiaoqu }}</div>
      <img src="/di02.png" alt="" />
      <span class="bottom">小区（个）</span>
    </div>
    <div class="single" @click="openBox(3)">
      <div class="top">{{ props.householder }}</div>
      <img src="/di02.png" alt="" />
      <span class="bottom">户数（户）</span>
    </div>
    <div class="single" @click="openBox(4)">
      <div class="top">{{ props.user_sum }}</div>
      <img src="/di02.png" alt="" />
      <span class="bottom">人数（人）</span>
    </div>
  </div>
  <div class="_box2">
    <div ref="huji" id="huji" @click="openBox(5)"></div>
    <div ref="renyuan" id="renyuan" @click="openBox(6)"></div>
    <div ref="zhengzhi" id="zhengzhi"></div>
    <div ref="sex" id="sex"></div>
  </div>
</template>

<script setup>
import * as echarts from 'echarts'
import { onMounted, reactive, ref, getCurrentInstance } from 'vue'
const emits = defineEmits(['increase'])
// 声明接收参数
const props = defineProps({
  householder: Number,
  user_sum: Number,
  dataHuJi: Array,
  dataType: Array,
  dataSex: Array,
  dataFace: Array,
  xiaoqu: Number,
})
const huji = ref(null)
const renyuan = ref(null)
const zhengzhi = ref(null)
const sex = ref(null)

onMounted(() => {
  setTimeout(() => {
    init1()
    init2()
    init3()
    init4()
  }, 1000)
})
// 户籍情况图表
function init1() {
  // console.log(props.dataHuJi);
  // // 获取页面的实例对象
  // const pageInstance = getCurrentInstance();
  // // 获取dom节点对象
  // const tagDomObj = pageInstance.refs.huji;
  const myEcharts = echarts.init(huji.value)

  // 计算总数
  // let total = props.data.reduce((p, v) => { return p + v.num }, 0)

  const colorList = [
    [
      { offset: 0, color: 'rgba(15, 105, 240)' },
      { offset: 1, color: 'rgba(15, 105, 240, 1)' },
    ],
    [
      { offset: 0, color: 'rgba(54, 234, 255, 1)' },
      { offset: 1, color: 'rgba(10, 165, 214, 1)' },
    ],
  ]

  const color = ['rgba(50, 255, 238, 1)', 'rgba(0, 233, 179, 1)', 'rgba(29, 246, 66, 1)', 'rgba(240, 255, 71, 1)', 'rgba(255, 213, 47, 1)', 'rgba(255, 126, 76, 1)', 'rgba(255, 96, 86, 1)', 'rgba(97, 187, 255, 1)']
  // data数据
  const echartData = props.dataHuJi.map((v, i) => ({
    name: v.name,
    value: v.num,
    itemStyle: {
      color: { type: 'linear', colorStops: colorList[i] },
    },
  }))

  // const totalData = props.data.map((v, i) => ({
  //   name: v.name,
  //   value: v.num,
  //   itemStyle: { color: colorList2[i] },
  // }))
  const series = [
    {
      type: 'pie',
      radius: ['30%', '60%'],
      center: ['50%', '50%'],
      selectedMode: false,
      hoverAnimation: false,
      top: 5,
      data: [{ value: 1, name: '' }],
      itemStyle: {
        color: '#0e2c5f',
      },
      label: {
        show: false,
      },
      labelLine: {
        show: false,
      },
      tooltip: {
        show: false,
      },
    },
    {
      name: '户籍情况',
      type: 'pie',
      radius: ['30%', '50%'],
      center: ['50%', '50%'],
      avoidLabelOverlap: false,
      labelLine: {
        length: 30, //第一段线长
        length2: 100, //第二段线长
      },
      top: 5,
      label: {
        formatter: function (pram) {
          return '{a|' + pram.data.name + ' }\n\n {b|' + pram.data.value + '}'
        },
        padding: [0, -110, -10, -110], //关键代码！关键代码！关键代码！
        rich: {
          a: {
            fontSize: 14,
            color: '#fff',
            fontWeight: 500,
            align: 'center',
          },
          b: {
            fontSize: 16,
            color: '#fff',
            fontWeight: 'bold',
            align: 'center',
          },
        },
      },
      data: echartData,
    },
  ]

  // 渲染
  var option = {
    title: {
      text: '户籍情况',
      left: 'center',
      textStyle: {
        color: '#fff',
      },
      top: -4,
    },
    series,
    color,
    backgroundColor: 'transparent',
  }
  option && myEcharts.setOption(option)
}
// 人员类型图表
function init2() {
  // // 获取页面的实例对象
  // const pageInstance = getCurrentInstance();
  // // 获取dom节点对象
  // const tagDomObj = pageInstance.refs.renyuan;
  const myEcharts = echarts.init(renyuan.value)

  // 计算总数
  // let total = props.data.reduce((p, v) => { return p + v.num }, 0)

  const colorList = [
    [
      { offset: 0, color: 'rgba(15, 105, 240, 1)' },
      { offset: 1, color: 'rgba(15, 105, 240, 1)' },
    ],
    [
      { offset: 0, color: 'rgba(54, 234, 255, 1)' },
      { offset: 1, color: 'rgba(10, 165, 214, 1)' },
    ],
  ]

  const color = ['rgba(50, 255, 238, 1)', 'rgba(0, 233, 179, 1)', 'rgba(29, 246, 66, 1)', 'rgba(240, 255, 71, 1)', 'rgba(255, 213, 47, 1)', 'rgba(255, 126, 76, 1)', 'rgba(255, 96, 86, 1)', 'rgba(97, 187, 255, 1)']
  // data数据
  const echartData = props.dataType.map((v, i) => ({
    name: v.name,
    value: v.num,
    itemStyle: {
      color: { type: 'linear', colorStops: colorList[i] },
    },
  }))

  // const totalData = props.data.map((v, i) => ({
  //   name: v.name,
  //   value: v.num,
  //   itemStyle: { color: colorList2[i] },
  // }))
  const series = [
    {
      type: 'pie',
      radius: ['30%', '60%'],
      center: ['50%', '50%'],
      selectedMode: false,
      hoverAnimation: false,
      top: 5,
      data: [{ value: 1, name: '' }],
      itemStyle: {
        color: '#0e2c5f',
      },
      label: {
        show: false,
      },
      labelLine: {
        show: false,
      },
      tooltip: {
        show: false,
      },
    },
    {
      name: '人员类型',
      type: 'pie',
      top: 5,
      radius: ['30%', '50%'],
      center: ['50%', '50%'],
      avoidLabelOverlap: false,
      labelLine: {
        length: 30, //第一段线长
        length2: 80, //第二段线长
      },
      label: {
        formatter: function (pram) {
          console.log(pram)
          return '{a|' + pram.data.name + ' }\n\n {b|' + pram.data.value + '}'
        },
        padding: [0, -80, -10, -80], //关键代码！关键代码！关键代码！
        rich: {
          a: {
            fontSize: 14,
            color: '#fff',
            fontWeight: 500,
            align: 'center',
          },
          b: {
            fontSize: 16,
            color: '#fff',
            fontWeight: 'bold',
            align: 'center',
          },
        },
      },
      data: echartData,
    },
  ]

  // 渲染
  var option = {
    title: {
      text: '人员类型',
      left: 'center',
      textStyle: {
        color: '#fff',
      },
      top: -4,
    },
    series,
    color,
    backgroundColor: 'transparent',
  }
  option && myEcharts.setOption(option)
}
//政治面貌图表
function init3() {
  // // 获取页面的实例对象
  // const pageInstance = getCurrentInstance();
  // // 获取dom节点对象
  // const tagDomObj = pageInstance.refs.zhengzhi;
  const myEcharts = echarts.init(zhengzhi.value)
  const total = ref(0)
  total.value = props.dataFace.reduce((pre, v) => {
    return (pre += v.value)
  }, 0)

  function getArrByKey(data, k) {
    let key = k || 'value'
    let res = []
    if (data) {
      data.forEach(function (t) {
        res.push(t[key])
      })
    }
    return res
  }
  function getSymbolData(data) {
    let arr = []
    for (var i = 0; i < data.length; i++) {
      arr.push({
        value: data[i].value,
        symbolPosition: 'end',
      })
    }
    return arr
  }
  var option = {
    title: {
      text: '政治面貌',
      textStyle: {
        color: '#fff',
      },
      left: 110,
    },
    backgroundColor: 'transparent',
    grid: {
      top: '20%',
      bottom: 0,
      // right: 30,
      left: 30,
      containLabel: true,
    },
    xAxis: {
      show: false,
    },
    yAxis: [
      {
        triggerEvent: true,
        show: true,
        inverse: true,
        data: getArrByKey(props.dataFace, 'name'),
        axisLine: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
          interval: 0,
          color: 'red',
          align: 'left',
          margin: 80,
          fontSize: 13,
          formatter: function (value, index) {
            return '{title|' + value + '}'
          },
          rich: {
            title: {
              width: 165,
            },
          },
        },
      },
      {
        triggerEvent: true,
        show: true,
        inverse: true,
        data: getArrByKey(props.dataFace, 'name'),
        axisLine: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        // 右侧的数值
        axisLabel: {
          interval: 0,
          shadowOffsetX: '-20px',
          color: '#0f72fe',
          fontWeight: 'bold',
          align: 'right',
          verticalAlign: 'bottom',
          lineHeight: 30,
          fontSize: 14,
          fontStyle: 'italic',
          formatter: function (value, index) {
            return ((props.dataFace[index].value / total.value) * 100).toFixed(1) + '%'
          },
        },
      },
    ],
    series: [
      //   {
      //   name: 'XXX',
      //   type: 'pictorialBar',
      //   symbol: 'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADoAAAA6CAMAAADWZboaAAAAZlBMVEUAAABe3uVe3+Vf3uVf3+Zf3uVg3+Zg3+Zf3+Vi4OZh4OZg3+Z86/Bh3+Zi4Odj4Odi4OZ86/B76/B86/Bj4ed56+9x5+xn4umB7/N87PB36e+A7/N+7fF/7vJ/7vJ+7fGA7/OB7/PReX+lAAAAIXRSTlMABQkVDREmIhk3MR10LEFFPHh7cUprXE35h2XnqMLAp+mHAG9cAAAB5ElEQVRIx83WjU7CMBQFYIoiKMqU/XUboHv/l/Tce7t2XamDNSacETEmX86tlK2rx4py150o+MstMBLwWRfHKo6JCVxLnvmFGBjFQ58oF1//sUZhGy/ClSTWObgnL4O+bkeN4nY2okfNMbkRt9/vtxz8InoTsWplJSCzFxPmO8+GpSIByX3YQAuGDWtRKhKjCnxDXhF6Z4yxnZ20Wgko7BMRDmxtSGVaI4kdTIgb+zTYoJQlIMlDlmUFgrcDWWC201qSayqlTkiCddWWeV62VU0YlnpRi9VOKaSUsiyq/N0krwq2Ugt7lVpZl5BfHNiytjagMi+XYp0kCR45hMlivVQrE/uU5pXSrCB5bM6d1t2lOZItMqmliT3q5uVxqxzyW/ccfYLNKx7ZTeykMvNyac2yt2Fbc61MHLSC0rwoxbiNdlQ3GBm1NLHQsHUrtEXppR/ljNpW6DbSCoqlFiVoN6YdaFlgsSFVPs1BdT8OaB5QyQzVcaqWDows/zepxR8ObLglTrdtCRVuRNj4Rrxh+//0ke2f8KVL+Kon3GCSbmsJN9OUW3j6g0Ns+LgCij2u0h+Sghc8mlMPBMgdx5DFh59VmOVHrvmDnoNxCz3J7MFWsMuaLyR089xz/xhlfijvwutR8gv3zk6BLUUeCgAAAABJRU5ErkJggg==',
      //   symbolSize: [60, 60],
      //   symbolOffset: [30, 0],
      //   z: 12,
      //   itemStyle: {
      //     color: '#fff'
      //   },
      //   data: getSymbolData(props.dataFace)
      // },
      {
        name: '条',
        type: 'bar',
        showBackground: true,
        backgroundStyle: {
          borderRadius: 30,
        },
        barBorderRadius: 30,
        yAxisIndex: 0,
        data: props.dataFace,
        barWidth: 9,
        // align: left,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            1,
            0,
            [
              {
                offset: 0,
                color: '#0f71fb ',
              },
              {
                offset: 1,
                color: '#81eff3',
              },
            ],
            false
          ),
          barBorderRadius: 10,
          barBorderRadius: 4,
        },
        label: {
          show: true,
          color: '#fff',
          position: [0, '-20px'],
          textStyle: {
            fontSize: 14,
          },
          formatter: function (a, b) {
            return ` ${a.name}  {hou|${a.value}} 人`
          },
          rich: {
            hou: {
              fontWeight: 'bold',
              fontSize: 16,
            },
          },
        },
      },
    ],
  }
  option && myEcharts.setOption(option)
}
// 性别构成图表
function init4() {
  // // 获取页面的实例对象
  // const pageInstance = getCurrentInstance();
  // // 获取dom节点对象
  // const tagDomObj = pageInstance.refs.sex;
  const myEcharts = echarts.init(sex.value)

  // 计算总数
  let total = props.dataSex.reduce((p, v) => {
    return p + v.num
  }, 0)

  const colorList = [
    [
      { offset: 0, color: 'rgba(15, 114, 254, 1)' },
      { offset: 1, color: 'rgba(15, 114, 254, 1)' },
    ],
    [
      { offset: 0, color: 'rgba(54, 234, 255, 1)' },
      { offset: 1, color: 'rgba(10, 165, 214, 1)' },
    ],
  ]
  const colorList2 = ['rgba(15, 114, 254, .3)', 'rgba(0, 56, 75, 1)']

  const color = ['rgba(50, 255, 238, 1)', 'rgba(0, 233, 179, 1)', 'rgba(29, 246, 66, 1)', 'rgba(240, 255, 71, 1)', 'rgba(255, 213, 47, 1)', 'rgba(255, 126, 76, 1)', 'rgba(255, 96, 86, 1)', 'rgba(97, 187, 255, 1)']
  // data数据
  const echartData = props.dataSex.map((v, i) => ({
    name: v.name,
    value: v.num,
    itemStyle: {
      color: { type: 'linear', colorStops: colorList[i] },
    },
  }))

  const totalData = props.dataSex.map((v, i) => ({
    name: v.name,
    value: v.num,
    itemStyle: { color: colorList2[i] },
  }))
  const series = [
    {
      name: '性别比例',
      type: 'pie',
      center: ['50%', '50%'],
      radius: [0, '50%'],
      top: 20,
      label: {
        fontSize: 14,
        position: 'inner',
        formatter: (params) => {
          return `${params.name}\n\n${params.value}`
        },
        color: 'white',
      },
      selectedMode: 'single',
      data: totalData,
    },
    {
      name: '性别比例',
      type: 'pie',
      top: 20,
      radius: ['50%', '80%'],
      center: ['50%', '50%'],
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: 'inside',
        fontSize: 16,
        formatter: (params) => {
          let percent = (params.data.value / total) * 100
          return `${percent.toFixed(0)}%`
        },
        color: 'white',
        fontWeight: 'bold',
      },
      data: echartData,
    },
  ]

  // 渲染
  var option = {
    title: {
      text: '性别构成',
      left: 'center',
      textStyle: {
        color: '#fff',
      },
    },
    series,
    color,
    backgroundColor: 'transparent',
  }
  option && myEcharts.setOption(option)
}
function openBox(v) {
  switch (v) {
    case 6:
      emits('dialogRenYuan')
      break
    case 5:
      emits('dialogHuJi')
      break
    case 4:
      emits('dialog2')
      break
    case 3:
      emits('dialogHu')
      break
    case 2:
      emits('dialogCommunity')
      break
    case 1:
      emits('dialogBranch')
      break
    default:
      break
  }
}
</script>

<style lang="scss" scoped>
._box {
  display: flex;
  justify-content: space-around;
  width: 100%;
  height: 130px;

  .single {
    color: #fff;
    width: 25%;
    text-align: center;
    position: relative;

    .top {
      width: 100%;
      font-size: 24px;
      position: absolute;
      top: 20px;
      text-align: center;
      font-weight: bold;
      background-image: -webkit-linear-gradient(bottom, white, #77f4f5);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    img {
      width: 100%;
    }

    .bottom {
      font-size: 16px;
    }
  }
}

._box2 {
  width: 100%;
  height: 510px;
  position: relative;

  #huji {
    width: 250px;
    height: 260px;
    position: absolute;
    left: 10px;
    top: 5%;
  }

  #renyuan {
    width: 250px;
    height: 260px;
    position: absolute;
    left: 50%;
    top: 5%;
  }

  #zhengzhi {
    width: 340px;
    height: 250px;
    position: absolute;
    left: -19px;
    top: 60%;
  }

  #sex {
    width: 250px;
    height: 250px;
    position: absolute;
    left: 50%;
    top: 60%;
  }
}
</style>
