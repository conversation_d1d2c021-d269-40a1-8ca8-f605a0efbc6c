<template>
  <div class="login" ref="Area">
    <div class="left">
      <div class="p1">智慧则南中央数据管理平台</div>
      <div class="p2">欢迎登录</div>
    </div>
    <div class="right">
      <div class="tit">账号登陆</div>
      <input type="text" v-model="userName" placeholder="请输入账号" class="ipt1" @blur="input1">
      <img src="/user.png" alt="" class="user">
      <div class="not1" v-show="is1">请输入账号！</div>
      <input type="password" v-model="password" placeholder="请输入密码" class="ipt2" @blur="input2" data="passworedtype">
      <img src="/password.png" alt="" class="password">
      <div class="not2" v-show="is2">请输入密码！</div>
      <div class="btn" @click="login">登录</div>
    </div>
  </div>
</template>

<script setup>
import * as THREE from 'three'
import CLOUDS from 'vanta/src/vanta.waves'
import Clouds from "vanta/src/vanta.clouds"
import Clouds2 from "vanta/src/vanta.clouds2"
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
const router = useRouter()
const userName = ref('')
const password = ref('')
const is1 = ref(false)
const is2 = ref(false)
const Area = ref(null)
let vantaEffect = null;
onMounted(() => {
  vantaEffect = Clouds({
    el: Area.value,
    THREE: THREE,
    //如果需要改变样式，要写在这里
    //因为这里vantaEffect是没有setOptions这个方法的
    // color: 0x16212a,
    skyColor: 0x5ca6ca,
    cloudShadowColor: 0x5c6c7a,
    speed: 2,
  })
})
function input1() {
  if (userName.value) {
    is1.value = false
  } else {
    is1.value = true
  }
}
function input2() {
  if (password.value) {
    is2.value = false
  } else {
    is2.value = true
  }
}
function login() {
  if (is1.value || is2.value) return
  if (userName.value && password.value) {
    if (userName.value == 'admin' && password.value == 'admin') {
      router.replace('/home')
    } else {
      ElMessage.error('请输入正确的账号和密码！')
    }
  }

  // localStorage.setItem('token', token)
}
onBeforeUnmount(() => {
  if (vantaEffect) {
    vantaEffect.destroy()
  }
})
</script>

<style scoped lang="scss">
// @font-face {
//   font-family: 'ziti';
//   /* 重命名字体名 */
//   src: url('/zihun.TTF');
// }

.login {
  width: 100%;
  height: 100%;
  // background: url(/bg.png) no-repeat;
  position: relative;

  .left {
    width: 956px;
    height: 240px;
    background: url(/ju.png) no-repeat;
    background-size: 100%;
    position: absolute;
    top: 430px;
    left: 99px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .p1 {
      font-size: 44px;
      // font-family: ziti;
      font-weight: 400;
      color: #FFFFFF;
      margin-bottom: 20px;
    }

    .p2 {
      font-size: 24px;
      // font-family: Source Han Sans CN;
      color: #FFFFFF;
      opacity: 0.8;
    }
  }

  .right {
    width: 499px;
    height: 489px;
    background: url(/ju2.png) no-repeat;
    background-size: 100%;
    position: absolute;
    top: 306px;
    right: 150px;

    .tit {
      font-size: 24px;
      font-weight: 500;
      color: #FFFFFF;
      position: relative;
      top: 80px;
      left: 50px;
    }

    .ipt1 {
      width: 360px;
      height: 60px;
      background: transparent;
      border: 2px solid white;
      position: relative;
      top: 100px;
      left: 50px;
      padding-left: 40px;
      font-size: 18px;
      color: white;
    }

    .ipt2 {
      width: 360px;
      height: 60px;
      background: transparent;
      border: 2px solid white;
      position: relative;
      top: 130px;
      left: 50px;
      padding-left: 40px;
      font-size: 18px;
      color: white;
    }


    .user {
      position: relative;
      top: 100px;
      right: 340px;
    }

    .password {
      position: relative;
      top: 130px;
      right: 340px;
    }

    .btn {
      text-align: center;
      font-size: 18px;
      color: white;
      width: 360px;
      height: 60px;
      background: #0070B7;
      border-radius: 4px;
      line-height: 60px;
      position: relative;
      top: 200px;
      left: 70px;
      cursor: pointer;
    }

    .not1 {
      color: red;
      position: absolute;
      top: 200px;
      left: 50px;
      height: 14px;
      font-weight: bold;
      font-size: 14px;
    }

    .not2 {
      color: red;
      position: absolute;
      top: 300px;
      left: 50px;
      height: 14px;
      font-weight: bold;
      font-size: 14px;
    }
  }
}

::-webkit-input-placeholder {
  /* WebKit browsers，webkit内核浏览器 */
  color: #ccc;
  font-size: 16px;
}
</style>