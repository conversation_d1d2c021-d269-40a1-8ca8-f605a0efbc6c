<template>
  <div class="_box">
    <div class="single" @click="showBox(0)">
      <div class="top">{{ 2 }}</div>
      <img src="/di02.png" alt="" />
      <span class="bottom">党支部(个)</span>
    </div>
    <div class="single" @click="showBox(1)">
      <div class="top">{{ props.gridNum }}</div>
      <img src="/di02.png" alt="" />
      <span class="bottom">网格数(个)</span>
    </div>
    <div class="single" @click="showBox(2)">
      <div class="top">{{ props.xiaoqu }}</div>
      <img src="/di02.png" alt="" />
      <span class="bottom">小组(个)</span>
    </div>
    <div class="single" @click="showBox(3)">
      <div class="top">{{ props.householder }}</div>
      <img src="/di02.png" alt="" />
      <span class="bottom">总户数(户)</span>
    </div>
    <div class="single" @click="showBox(4)">
      <!-- <div class="top">{{ props.user_sum }}</div> -->
      <div class="top">7394</div>
      <img src="/di02.png" alt="" />
      <span class="bottom">总人数(人)</span>
    </div>
  </div>
</template>

<script setup>
const emits = defineEmits(['increase'])
// 声明接收参数
const props = defineProps({
  householder: Number,
  user_sum: Number,
  xiaoqu: Number,
  gridNum: Number,
})

// 弹窗展示
function showBox(v) {
  switch (v) {
    case 4:
      emits('dialog2')
      break
    case 3:
      emits('dialogHu')
      break
    case 2:
      emits('dialogCommunity')
      break
    case 0:
      emits('dialogBranch')
      break
    default:
      break
  }
}
</script>

<style lang="scss" scoped>
._box {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  height: 130px;

  .single {
    color: #fff;
    width: 27%;
    text-align: center;
    position: relative;
    margin: 10px;
    cursor: pointer;

    .top {
      width: 100%;
      font-size: 24px;
      position: absolute;
      top: 20px;
      text-align: center;
      font-weight: bold;
      background-image: -webkit-linear-gradient(bottom, white, #77f4f5);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    img {
      width: 130px;
    }

    .bottom {
      font-size: 16px;
    }
  }
}

._box2 {
  width: 100%;
  height: 374px;
  position: relative;
}
</style>
