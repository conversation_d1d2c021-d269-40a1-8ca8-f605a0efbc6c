import http from '../plugins/http.js'
import https from '../plugins/https.js'

//首页轮播
// export function login(parms) {
//   return http.post('/banner', parms)
// }

export function statisticalData(parms) {
  return http.get('/v1/ioc.user.statisticalData', parms)
}

export function getWeather(parms) {
  return http.get('https://tianqiapi.com/free/day', parms)
}

export function specialUserData(parms) {
  return http.get('/v1/ioc.user.specialUserData', parms)
}
export function famousUserData(parms) {
  return http.get('/v1/ioc.user.famousUserData', parms)
}
export function focusUserData(parms) {
  return http.get('/v1/ioc.user.focusUserData', parms)
}
// 经济结构
export function government(parms) {
  return http.get('/v1/ioc.economic.query', parms)
}
// 小区
export function getCommunity(parms) {
  return http.get('/v1/ioc.community.profile.queryVillageInfo', parms)
}
// 总户数
export function getHousehold(parms) {
  return http.get('/v1/ioc.community.profile.queryHouseholdInfo', parms)
}
// 总人数  户籍情况
export function getAllPeopleHuJi(parms) {
  return http.get('/v1/ioc.community.profile.queryNumByDomicileType', parms)
}
// 人员类型
export function getAllPeopleType(parms) {
  return http.get('/v1/ioc.community.profile.queryNumByPersonnelType', parms)
}
// 人数详情
export function getAllPersonal(parms) {
  return http.get('/v1/ioc.community.profile.queryPersonnelInfo', parms)
}
// 年龄结构
export function getAge(parms) {
  return http.get('/v1/ioc.community.personnel.queryAgeStructureInfo', parms)
}
// 学生构成
export function getStudent(parms) {
  return http.get('/v1/ioc.community.personnel.queryStudents', parms)
}
// 重点监管
export function getFocusPerson(parms) {
  return http.get('/v1/ioc.community.personnel.queryFocusCrowd', parms)
}
// 特殊人群

export function getSpecialPerson(parms) {
  return http.get('/v1/ioc.community.personnel.querySpecialPopulationsInfo', parms)
}
// 军人构成
export function getSoldierPerson(parms) {
  return http.get('/v1/ioc.community.personnel.querySoldierInfo', parms)
}
// 民意互动情况
export function getInteration(parms) {
  return https.get('/ioc/userMessage/count', parms)
}
// 社会名人
export function getFamousPerson(parms) {
  return http.get('/v1/ioc.community.personnel.queryFamousInfo', parms)
}
