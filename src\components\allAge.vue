<template>
  <div class="dialog">
    <div class="title">
      <span>年龄构成</span>
    </div>
    <div class="tabs">
      <div class="tab">
        <div v-for="(item, index) in ageMap" class="single" @click="changeIs(index)">
          <div :style="is == index ? { 'font-weight': 'bold' } : {}">{{ item.name }}({{ item.value }}人)</div>
          <img src="/selected.png" alt="" v-show="is == index" />
        </div>
      </div>
      <div class="close" @click="handelClick">
        <img src="/goback.png" alt="" />
        <span> 返回</span>
      </div>
    </div>
    <table border="0">
      <thead>
        <th>序号</th>
        <th @click="searchName">
          姓名
          <img src="../assets/search.png" class="searchs" />
        </th>
        <th>性别</th>
        <th>身份证</th>
        <!-- <th>职业</th> -->
        <th>电话</th>
        <th>小区</th>
        <th>住址</th>
        <th>网格</th>
      </thead>
      <tbody class="tb1" v-if="tableData.length">
        <tr v-for="(v, index) in tableData" :key="v.id">
          <td>{{ index + 1 }}</td>
          <td>{{ v.name }}</td>
          <td>{{ v.gender }}</td>
          <td>{{ v.id_card }}</td>
          <td>{{ v.phone }}</td>
          <td>{{ v.address }}</td>
          <td>{{ v.room }}</td>
          <td>{{ v.grid_name }}</td>
        </tr>
      </tbody>
      <tbody v-else>
        <p class="zan">暂无数据！</p>
      </tbody>
    </table>
    <div class="pagination-block">
      <el-pagination layout="prev, pager, next" :total="total" style="--el-pagination-font-size: 16px; --el-pagination-button-disabled-bg-color: none; --el-pagination-bg-color: none; --el-pagination-button-color: white" :current-page="currentPage" :page-size="pageSize" @current-change="handleCurrentChange" />
    </div>
    <div class="searchBox" v-show="showSearch">
      <div class="ipt">姓名 <input type="text" v-model="userName" placeholder="请输入姓名" class="ipt1" @blur="input1" /></div>
      <div class="btns">
        <div @click="searchData">搜索</div>
        <div @click="showSearch = !showSearch">取消</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getAge } from '../api'
import * as echarts from 'echarts'
import { ref, onMounted, reactive, getCurrentInstance } from 'vue'
import { statisticalData } from '../api'
import { useRoute } from 'vue-router'
const emits = defineEmits(['closeAge'])
// 声明接收参数
const props = defineProps({
  dataAge: Array,
  focusAge: Number,
})
// //  点击事件
const handelClick = () => {
  console.log('触发子组件点击')
  // 触发父组件事件
  emits('closeAge')
}
const route = useRoute()
let is = ref(1)
let showSearch = ref(false)
let tableData = ref([])
let ageMap = ref([])
let b = ref(0)
let e = ref(0)
let userName = ref('')

let total = ref(1)
let currentPage = ref(1)
let pageSize = ref(15)

function changeIs(val) {
  is.value = val
  let routes = route
  let code = routes.query.code
  console.log(val)
  switch (is.value) {
    case 0:
      b.value = 0
      e.value = 3
      break
    case 1:
      b.value = 4
      e.value = 6
      break
    case 2:
      b.value = 7
      e.value = 12
      break
    case 3:
      b.value = 13
      e.value = 18
      break
    case 4:
      b.value = 19
      e.value = 59
      break
    case 5:
      b.value = 60
      e.value = 80
      break
    default:
      b.value = 80
      e.value = 200
      break
  }
  getAges()
}
function searchName() {
  showSearch.value = true
}
// 搜索
function searchData() {
  currentPage.value = 1
  setTimeout(() => {
    getAges()
    searchCancel()
  }, 100)
}
function searchCancel() {
  showSearch.value = false
  userName.value = ''
}
// 分页
function handleCurrentChange(v) {
  currentPage.value = v
  getAges()
}
function getAges() {
  getAge({
    limit: 15,
    offset: (currentPage.value / 1 - 1) * 15,
    beginAge: b.value,
    endAge: e.value,
    name: userName.value,
    type: route.query.code ? (route.name == 'a' ? '2' : '3') : '1',
    code: route.query.code ? route.query.code : '',
  }).then((res) => {
    // console.log(res)
    total.value = res.data.count
    tableData.value = res.data.rows
  })
}
onMounted(() => {
  let types = route.query.code ? (route.name == 'a' ? '2' : '3') : '1'
  let codes = route.query.code ? route.query.code : ''
  statisticalData({
    type: types,
    code: codes,
  }).then((res) => {
    ageMap.value = res.data.age
    changeIs(props.focusAge)
  })
})
</script>

<style scoped lang="scss">
.zan {
  position: absolute;
  font-size: 16px;
  color: white;
  top: 30%;
  width: 100%;
  text-align: center;
}
.dialog {
  width: 100%;
  height: 740px;
  background-color: #07192a;
  position: relative;

  .title {
    width: 100%;
    text-align: center;
    height: 38px;
    outline: 1px solid #0c3f7e;
    background: url(/title.png) no-repeat;
    background-position: 50%;
    font-size: 20px;
    font-weight: bold;
    color: #4a97f1;

    span {
      vertical-align: middle;
    }
  }

  .tabs {
    height: 53px;
    position: relative;
    outline: 1px solid #0c3f7e;

    .tab {
      width: 1100px;
      text-align: center;
      font-size: 18px;
      // font-weight: bold;
      color: #ffffff;
      display: flex;

      .single {
        width: 170px;
        height: 35px;
        margin-top: 10px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        cursor: pointer;
      }
    }

    .close {
      position: absolute;
      color: #4a97f1;
      cursor: pointer;
      right: 19px;
      top: 35%;
      font-size: 16px;
    }
  }
}
// 表格
table {
  width: 98%;
  height: 80%;
  margin: 10px auto;
  border: 2px solid #0c3f7e;
  border-collapse: collapse;
}

table thead {
  background-color: #0a2846;
  height: 34px;

  th {
    font-size: 18px;
    color: #4a97f1;
  }

  th:nth-child(1) {
    width: 50px;
  }

  th:nth-child(2) {
    cursor: pointer;
    width: 100px;
  }

  th:nth-child(3) {
    width: 50px;
  }

  th:nth-child(4) {
    width: 250px;
  }

  th:nth-child(5) {
    width: 150px;
  }

  th:nth-child(7) {
    width: 150px;
  }

  th:nth-child(8) {
    width: 300px;
  }
}

table tbody {
  max-height: 570px;
  display: block;
  overflow-y: auto;

  tr:nth-child(even) {
    background: #0e2947;
    height: 38px;

    td {
      text-align: center;
      color: white;
      font-size: 16px;
    }

    td:nth-child(1) {
      width: 50px;
    }

    td:nth-child(2) {
      width: 100px;
    }

    td:nth-child(3) {
      width: 50px;
    }

    td:nth-child(4) {
      width: 250px;
    }

    td:nth-child(5) {
      width: 150px;
    }

    td:nth-child(7) {
      width: 150px;
    }

    td:nth-child(8) {
      width: 300px;
    }
  }

  tr:nth-child(odd) {
    height: 38px;

    td {
      text-align: center;
      color: white;
      font-size: 16px;
    }

    td:nth-child(1) {
      width: 50px;
    }

    td:nth-child(2) {
      width: 100px;
    }

    td:nth-child(3) {
      width: 50px;
    }

    td:nth-child(4) {
      width: 250px;
    }

    td:nth-child(5) {
      width: 150px;
    }

    td:nth-child(7) {
      width: 150px;
    }

    td:nth-child(8) {
      width: 300px;
    }
  }
}

table thead,
table tbody tr {
  width: 100%;
  display: table;
  table-layout: fixed;
}

table thead {
  width: calc(100% - 3px);
}

table .tb1::-webkit-scrollbar {
  width: 10px !important;
}

::-webkit-scrollbar {
  width: 10px !important;
  height: 16px !important;
  background-color: rgba(21, 40, 79, 0.26);
  border-radius: 10px;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: rgba(21, 40, 79, 0.26);
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #234674;
}
.searchs {
  width: 20px;
}
.searchBox {
  width: 500px;
  height: 140px;
  background-color: black;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 30%;
  font-size: 20px;
  border-radius: 20px;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  color: white;

  .btns {
    display: flex;
    div {
      width: 100px;
      text-align: center;
      background-color: #4a97f1;
      height: 50px;
      line-height: 50px;
      border-radius: 15px;
      margin: 0 20px;
      cursor: pointer;
    }
  }
}
.ipt1 {
  width: 200px;
  height: 40px;
  background: transparent;
  border: 2px solid white;
  font-size: 18px;
  color: white;
  text-align: center;
}
.pagination-block {
  position: absolute;
  right: 0;
  bottom: 2.5px;
}
</style>
