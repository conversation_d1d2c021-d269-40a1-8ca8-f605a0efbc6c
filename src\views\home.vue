<template>
  <!-- <div class="goback" @click="$router.replace('/home')" style="font-size: 12px">
    <span class="backText">&emsp;返回</span>
  </div> -->
  <div class="qiu" @click="showAll">
    <img src="/zhankai.png" v-show="$store.state.show.zhan" alt="" />
    <img src="/shousuo.png" v-show="!$store.state.show.zhan" alt="" />
  </div>
  <div class="biao">树仁社区</div>
  <div class="groups">
    <div class="group_item" v-for="item in data.groups" :key="item.id">{{ item.name }}</div>

  </div>
  <!-- <iframe :src="map" frameborder="0" class="map"></iframe> -->
  <div class="left" v-if="!$store.state.show.zhan">
    <div class="one">
      <div class="title">
        <span>社区概况</span>
      </div>
      <div class="one_dis">
        <streetData :gridNum="data.gridNum" :xiaoqu="data.xiaoqu" :householder="data.householder" :user_sum="data.user_sum" @dialog2="showDialog2" @dialogHu="showDialogHu" @dialogCommunity="showDialogCommunity" @dialogBranch="showDialogBranch"> </streetData>
      </div>
    </div>
    <div class="two">
      <div class="title"><span>经济结构</span></div>
      <div class="two_dis">
        <bussinessCase @dialogBussiness="showDialog"></bussinessCase>
      </div>
    </div>
    <div class="three">
      <div class="title"><span>隐患类型</span></div>
      <div class="three_dis">
        <safeData :safeData="data.safe"></safeData>
      </div>
    </div>
  </div>
  <div class="right" v-if="!$store.state.show.zhan">
    <div class="one">
      <div class="title">
        <span>社区人员</span>
        <!-- <img src="/more.png" alt="" @click="showDialog3" class="more" /> -->
      </div>
      <div class="one_dis">
        <peopleData :dataAge="data.dataAge" :dataPerson="data.dataPerson" :dataSpecial="data.dataSpecial" :dataImportant="data.dataImportant" :subObj="data.subObj" @dialog="showDialog" @dialogAge="showDialogAge"> </peopleData>
      </div>
    </div>
    <div class="two">
      <div class="title"><span>社区服务</span></div>
      <div class="two_dis">
        <serviceCase :dataService="data.dataService"></serviceCase>
      </div>
    </div>
  </div>
  <div class="bottom" v-show="!$store.state.show.zhan">
    <div class="title1"><span>邻里心声互动情况统计</span></div>
    <div class="bot_dis">
      <interative :dataInteraction="data.dataInteraction"></interative>
    </div>
  </div>
  <div class="mengban" v-show="$store.state.sheQu.isClosed"></div>
  <div class="dialog" v-show="$store.state.sheQu.isClosed1">
    <!-- 社会名人 -->
    <tableVue @close="closeDialog" v-if="$store.state.sheQu.no === 1" :type="'1'" :code="''"></tableVue>
    <!-- 特殊人群 -->
    <tableVue2 @close="closeDialog" v-if="$store.state.sheQu.no === 2" :type="'1'" :code="''"></tableVue2>
    <!-- 重点监控人群 -->
    <tableVue3 @close="closeDialog" v-if="$store.state.sheQu.no === 3" :type="'1'" :code="''"></tableVue3>
    <!-- 经济结构 事业单位 -->
    <tableVue4 @close="closeDialog" v-if="$store.state.sheQu.no === 4" :type="'1'" :code="''"></tableVue4>
  </div>
  <!-- 总人数 -->
  <div class="dialog" v-if="$store.state.sheQu.isClosed2">
    <allPeople @close2="closeDialog2"></allPeople>
  </div>
  <!-- 总户数 -->
  <div class="dialog" v-if="$store.state.sheQu.isClosedHu">
    <allHouseHolds @closeHu="closeDialogHu"></allHouseHolds>
  </div>
  <!-- 总小区数 -->
  <div class="dialog" v-show="$store.state.sheQu.isClosedCommunity">
    <allCommunity @closeCommunity="closeDialogCommunity"></allCommunity>
  </div>
  <!-- 党支部 -->
  <div class="dialog" v-show="$store.state.sheQu.isClosedBranch">
    <allBranch @closeBranch="closeDialogBranch"></allBranch>
  </div>
  <!-- 年龄构成 右上角 -->
  <div class="dialog" v-if="$store.state.sheQu.isClosed3">
    <resident @close3="closeDialog3" :dataSolier="data.dataSolier" :dataStudent="data.dataStudent"></resident>
  </div>
  <!-- 年龄构成  -->
  <div class="dialog" v-if="$store.state.sheQu.isClosedAge">
    <allAge @closeAge="closeDialogAge" v-if="$store.state.sheQu.updateData" :focusAge="data.focusAge || $store.state.sheQu.age"></allAge>
  </div>
</template>
<script setup>
import { ElMessage } from 'element-plus'
import { statisticalData, getHousehold } from '../api'
import allPeople from '../components/allPeople.vue'
import allHouseHolds from '../components/allHouseHolds.vue'
import allCommunity from '../components/allCommunity.vue'
import allBranch from '../components/allBranch.vue'
import allAge from '../components/allAge.vue'
import resident from '../components/resident.vue'
import tableVue from '../components/table.vue'
import tableVue2 from '../components/table2.vue'
import tableVue3 from '../components/table3.vue'
import tableVue4 from '../components/table4.vue'
import interative from '../components/interative.vue'
import serviceCase from '../components/serviceCase.vue'
import bussinessCase from '../components/bussinessCase.vue'
import streetData from '../components/streetData.vue'
import peopleData from '../components/peopleData.vue'
import safeData from '../components/safeData.vue'
import { onMounted, onUnmounted, ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex' // 引入useStore 方法
const store = useStore() // 该方法用于返回store 实例
const router = useRouter()
const map = ref('map')
const suo = ref(true)
const no = ref(1)
let updateData = ref(false)
const isClosed = ref(false)
const isClosed1 = ref(false)
const isClosed2 = ref(false)
const isClosed3 = ref(false)
const isClosed4 = ref(false)
const isClosedHu = ref(false)
const isClosedCommunity = ref(false)
const isClosedBranch = ref(false)
const isClosedAge = ref(false)
let data = reactive({
  groups:[
    {name:'第1居民小组'},
    {name:'第2居民小组'},
    {name:'第3居民小组'},
    {name:'第4居民小组'},
    {name:'第5居民小组'},
  ],
  subObj: {},
  householder: 0,
  user_sum: 0,
  dataFrom: [],
  dataType: [],

  dataSex: [],
  dataFace: [],
  safe: [
    {
      frequency: 25,
      value1: 2,
      value2: '地质灾害',
    },
    {
      frequency: 45,
      value1: 5,
      value2: '防汛抗旱',
    },
    // {
    //   value1: 2,
    //   value2: '电安全隐患'
    // },
    {
      frequency: 15,
      value1: 3,
      value2: '气安全隐患',
    },
  ],
  dataService: [
    {
      sum: 26,
      name: '理论教育',
    },
    {
      sum: 6,
      name: '专业教育',
    },
    {
      sum: 2,
      name: '医疗服务',
    },
    {
      sum: 4,
      name: '就业教育',
    },
    {
      sum: 3,
      name: '文化教育',
    },
    {
      sum: 10,
      name: '社会服务',
    },
  ],
  dataStudent: [
    {
      name: '大学',
      value: 200,
    },
    {
      name: '高中',
      value: 150,
    },
    {
      name: '初中',
      value: 200,
    },
    {
      name: '小学',
      value: 100,
    },
  ],
  dataAge: [],
  dataPerson: [],
  dataSpecial: [],
  dataImportant: [],
  dataSolier: [],
  dataInteraction: {
    data1: [5, 8, 2, 20, 5, 10],
    data2: [10, 5, 3, 10, 5, 20],
    data3: [20, 6, 10, 5, 20, 6],
    data4: [10, 6, 20, 2, 10, 6],
  },
  focusAge: null,
  gridNum: 0,
  xiaoqu: 0,
})
onMounted(() => {
  getDeatil()
  window.addEventListener('message', fun)
  getHousehold({ limit: 15, offset: 0, type: '1', code: '', householder_name: '' }).then((res) => {
    data.householder = res.data.count
  })
})
async function getDeatil() {
  const res = await statisticalData({
    type: '1',
    code: '',
  })
  if (res.code == 200) {
    // data.householder = res.data.householder
    data.user_sum = res.data.user_sum
    data.dataFrom = res.data.domicile_from
    data.dataType = res.data.domicile_type
    data.dataSex = res.data.gender
    data.dataFace = res.data.political
    data.dataAge = res.data.age
    data.dataPerson = res.data.famous_type
    data.dataSpecial = res.data.special_group
    data.dataImportant = res.data.soldier_type
    data.dataSolier = res.data.soldier_type
    data.dataStudent = res.data.student
    data.xiaoqu = res.data.xiaoqu
    data.gridNum = res.data.gridNum
    data.subObj = {
      teenager: res.data.teenager,
      teenager_percent: res.data.teenager_percent,
      middle_aged: res.data.middle_aged,
      middle_aged_percent: res.data.middle_aged_percent,
      old_aged: res.data.old_aged,
      old_aged_percent: res.data.old_aged_percent,
    }
  } else {
    ElMessage.error(res.message)
  }
  // console.log(data.dataFrom);
}
function fun(event) {
  // console.log(event);
  let type = event.data.type
  if (type == 'wangge' && suo.value) {
    if (event.data.value == 'a') {
      // 望凤街
      suo.value = false
      router.replace({ path: '/communities', query: { code: '10' } })
    } else {
      // 中兴街
      suo.value = false
      router.replace({ path: '/communities', query: { code: '20' } })
    }
  }
}
function showAll() {
  store.dispatch('changeZhan')
}

// 控制弹窗
function showDialog(val) {
  // no.value = val
  store.state.sheQu.no =  val
  console.log('closed1',val);
  // isClosed.value = true
  // isClosed1.value = true
  store.dispatch('changeMask', true)
  store.dispatch('changeClosed1', true)
}
function closeDialog() {
  // isClosed.value = false
  // isClosed1.value = false
  store.dispatch('changeMask', false)
  store.dispatch('changeClosed1', false)
}
// 总人数
function showDialog2() {
  // isClosed.value = true
  // isClosed2.value = true
  store.dispatch('changeMask', true)
  store.dispatch('changeClosed2', true)
}
function closeDialog2() {
  // isClosed.value = false
  // isClosed2.value = false
  store.dispatch('changeMask', false)
  store.dispatch('changeClosed2', false)
}
// 总户数
function showDialogHu() {
  // isClosed.value = true
  // isClosedHu.value = true
  store.dispatch('changeMask', true)
  store.dispatch('changeClosedHu', true)
}
function closeDialogHu() {
  // isClosed.value = false
  // isClosedHu.value = false
  store.dispatch('changeMask', false)
  store.dispatch('changeClosedHu', false)
}
// 总小区数
function showDialogCommunity() {
  // isClosed.value = true
  // isClosedCommunity.value = true
  store.dispatch('changeMask', true)
  store.dispatch('changeClosedCommunity', true)
}
function closeDialogCommunity() {
  // isClosed.value = false
  // isClosedCommunity.value = false
  store.dispatch('changeMask', false)
  store.dispatch('changeClosedCommunity', false)
  
}
// 年龄构成 右上角
function showDialog3() {
  // isClosed.value = true
  // isClosed3.value = true
  store.dispatch('changeMask', true)
  store.dispatch('changeClosed3', true)
}
function closeDialog3() {
  // isClosed.value = false
  // isClosed3.value = false
  store.dispatch('changeMask', false)
  store.dispatch('changeClosed3', false)

}
// 党支部
function showDialogBranch() {
  // isClosed.value = true
  // isClosedBranch.value = true
  store.dispatch('changeMask', true)
  store.dispatch('changeClosedBranch', true)

}
function closeDialogBranch() {
  // isClosed.value = false
  // isClosedBranch.value = false
  store.dispatch('changeMask', false)
  store.dispatch('changeClosedBranch', false)
}
//  年龄构成
function showDialogAge(v) {
  // updateData.value = true
  data.focusAge = v
  // isClosed.value = true
  // isClosedAge.value = true
  store.dispatch('changeMask', true)
  store.dispatch('changeClosedAge', true)
  store.dispatch('changeClosedUpdate', true)
}
function closeDialogAge() {
  // updateData.value = false
  // isClosed.value = false
  // isClosedAge.value = false
  store.dispatch('changeMask', false)
  store.dispatch('changeClosedAge', false)
  store.dispatch('changeClosedUpdate', false)

}
onUnmounted(() => {
  map.value = 'map' + '?' + new Date().getTime()
  window.removeEventListener('message', fun)
})
</script>
<style scoped lang="scss">
.goback {
  width: 60px;
  height: 30px;
  background: url(/di01.png) no-repeat;
  background-size: 100% 100%;
  z-index: 2;
  position: absolute;
  left: 240px;
  top: 37px;
  color: white;
  text-align: center;
  line-height: 30px;
  cursor: pointer;

  .backText {
    vertical-align: middle;
    font-size: 16px;
  }
}

.qiu {
  z-index: 3;
  position: absolute;
  right: 250px;
  top: 40px;
  cursor: pointer;
}

.left {
  z-index: 1;
  width: 500px;
  height: 967px;
  top: 113px;
  position: absolute;

  .one {
    height: 361px;

    .one_dis {
      width: 100%;
      height: 311px;
    }
  }

  .two {
    height: 214px;

    .two_dis {
      height: 164px;
    }
  }

  .three {
    height: 392px;

    .three_dis {
      height: 342px;
    }
  }
}

.right {
  z-index: 1;
  width: 500px;
  height: 967px;
  position: absolute;
  right: 0;
  top: 113px;

  .one {
    height: 575px;

    .one_dis {
      height: 525px;
    }
  }

  .two {
    height: 392px;

    .two_dis {
      height: 342px;
    }
  }

  .three {
    .three_dis {
      height: 287px;
    }
  }
}

.bottom {
  width: 880px;
  height: 259px;
  position: absolute;
  z-index: 2;
  bottom: 0px;
  left: 500px;
}

.biao {
  width: 229px;
  height: 55px;
  position: absolute;
  left: 856px;
  top: 107px;
  background: url(/di04.png) no-repeat;
  background-size: 100% 100%;
  z-index: 2;
  color: white;
  font-size: 20px;
  text-align: center;
  line-height: 55px;
  letter-spacing: 3px;
}

.title {
  height: 50px;
  background: url(/biaoti_di.png) no-repeat;
  background-size: 100% 100%;
  margin: 0 20px;

  span {
    font-size: 22px;
    padding-left: 40px;
    font-weight: 600;
    line-height: 50px;
    color: #fff;
    letter-spacing: 5px;
  }

  .more {
    margin-left: 60%;
    cursor: pointer;
  }
}

.title1 {
  width: 880px;
  height: 50px;
  background: url(/biaoti_di1.png) no-repeat;
  background-size: contain;
  margin: 0 20px;

  span {
    font-size: 22px;
    padding-left: 40px;
    font-weight: 600;
    line-height: 50px;
    color: #fff;
    letter-spacing: 5px;
  }
}

.map {
  width: 100%;
  height: 100%;
}

.mengban {
  width: 1920px;
  height: 1080px;
  background-color: black;
  left: 0;
  opacity: 0.7;
  position: absolute;
  top: 0;
  z-index: 3;
  filter: alpha(opacity=70);
}

.dialog {
  z-index: 4;
  width: 1300px;
  height: 740px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid #0c3f7e;
}
.groups{
  position: absolute;
  top: 177px;
  height: auto;
  display: flex;
  width: 580px;
  flex-wrap: wrap;
  justify-content: space-between;
  left: 50%;
  transform: translateX(-50%);

  .group_item {
    width: 229px;
    height: 55px;
    background: url(/di04.png) no-repeat;
    background-size: 100% 100%;
    z-index: 2;
    color: white;
    font-size: 20px;
    text-align: center;
    line-height: 55px;
    letter-spacing: 3px;
  }

}
</style>
