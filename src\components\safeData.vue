<template>
  <div class="_box">
    <div class="single" v-for="item in props.safeData">
      <div class="top">
        <div class="safe">
          排查：<span class="text1">{{ item.frequency }}<span style="font-size: 12px">次</span>/月</span>
        </div>
        <div class="danger">
          隐患：<span class="text1">{{ item.value1 }}</span
          >个&emsp;整改：<span class="text1">{{ item.value1 }}</span
          >个
        </div>
      </div>
      <img src="/zhengdi.png" alt="" />
      <div class="bottom">{{ item.value2 }}</div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
const emits = defineEmits(['increase'])
// 声明接收参数
const props = defineProps({
  safeData: Array,
})

onMounted(() => {})
</script>

<style lang="scss" scoped>
._box {
  display: flex;
  // justify-content: space-around;
  width: 100%;
  height: 179px;
  position: relative;
  top: -24px;
  flex-wrap: wrap;

  .single {
    color: #fff;
    width: 45%;
    text-align: center;

    img {
      width: 112px;
    }

    .top {
      width: 100%;
      text-align: center;
      font-size: 16px;
      position: relative;
      top: 40px;

      .safe {
        .text1 {
          font-size: 18px;
          font-weight: bold;
          background-image: -webkit-linear-gradient(bottom, white, #77f4f5);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .danger {
        margin-top: 10px;
      }
    }

    .bottom {
      font-size: 16px;
      font-weight: bold;
      color: #06ece3;
    }
  }
}
</style>
