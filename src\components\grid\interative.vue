<template>
  <div ref="age" id="age"></div>
  <div class="subtext">
    <span class="name">咨询总计：</span><span class="n1">123</span>
    &emsp;
    <span class="name">诉求总计：</span><span class="n2">88</span>
    &emsp;
    <span class="name">隐患总计：</span><span class="n3">95</span>
    &emsp;
    <span class="name">善行总计：</span><span class="n4">66</span>
    &emsp;
    <span class="name">共计：</span><span class="n5">333</span>
  </div>
</template>

<script setup>
import * as echarts from "echarts";
import { onMounted, getCurrentInstance, ref } from 'vue';
// 声明组件要触发的事件
const emits = defineEmits(['increase']);
// 声明接收参数
const props = defineProps({
  dataInteraction: Object,
});
// //  点击事件
// const handelClick = () => {
//   console.log('触发子组件点击')
//   // 触发父组件事件
//   emits('increase')
// };
onMounted(() => {
  init()
})
// 年龄构成图表
function init() {
  // 获取页面的实例对象
  const pageInstance = getCurrentInstance();
  // 获取dom节点对象
  const tagDomObj = pageInstance.refs.age;
  const myEcharts = echarts.init(tagDomObj);
  var option;
  var xData = [
    '热点', '养老', '教育', '医疗', '就业', '安全', '生育', '特殊群体',
  ],
    // var min = 50;
    // props.dataAge.map(function (a, b) {
    //   xData.push(a.name);
    //   if (a.value === 0) {
    //     yData.push(a.value + min);
    //   } else {
    //     yData.push(a.value);
    //   }
    // });
    option = {
      backgroundColor: "transparent",
      color: ['#3398DB'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
          lineStyle: {
            opacity: 0
          }
        },
        backgroundColor: '#041a52',
        borderColor: '#65C1DC',
        textStyle: {
          color: 'white'
        }
        // formatter: function (prams) {
        //   console.log(prams);
        //   return "合格率：" + prams[0].data + "%"
        // }
      },
      legend: {
        left: 'center',
        data: ['咨询', '诉求', '建议', '表扬'],
        textStyle: {
          color: 'white',
          fontSize: 14
        },
      },
      grid: {
        left: '2%',
        right: '2%',
        top: '30%',
        containLabel: true,
      },
      xAxis: [{
        type: 'category',
        gridIndex: 0,
        data: xData,
        axisTick: {
          show: false
        },
        axisLine: {
          lineStyle: {
            color: '#A4DAFF'
          }
        },
        axisLabel: {
          show: true,
          color: 'white',
          fontSize: 14,
          interval: 0,//使x轴文字显示全
        }
      }],
      yAxis: {
        type: 'value',
        splitNumber: 4,
        interval: 10,
        splitLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        min: 0,
        max: 30,
        axisLine: {
          lineStyle: {
            color: '#A4DAFF',
          }
        },
        axisLabel: {
          color: 'white',
          formatter: '{value}'
        }
      },
      series: [
        {
          name: '咨询',
          type: 'bar',
          barWidth: '15%',
          xAxisIndex: 0,
          yAxisIndex: 0,
          itemStyle: {
            // barBorderRadius: [30, 30, 0, 0],
            // color: new echarts.graphic.LinearGradient(
            //   0, 0, 0, 1, [{
            //     offset: 0,
            //     color: '#00feff'
            //   },
            //   {
            //     offset: 0.5,
            //     color: '#027eff'
            //   },
            //   {
            //     offset: 1,
            //     color: '#0286ff'
            //   }
            // ]
            // )
            color: '#0fa3f1'
          },
          data: props.dataInteraction.data1,
        },
        {
          name: '诉求',
          type: 'bar',
          barWidth: '15%',
          xAxisIndex: 0,
          yAxisIndex: 0,
          itemStyle: {
            // barBorderRadius: [30, 30, 0, 0],
            // color: new echarts.graphic.LinearGradient(
            //   0, 0, 0, 1, [{
            //     offset: 0,
            //     color: '#00feff'
            //   },
            //   {
            //     offset: 0.5,
            //     color: '#027eff'
            //   },
            //   {
            //     offset: 1,
            //     color: '#0286ff'
            //   }
            // ]
            // )
            color: '#f4983c'
          },
          data: props.dataInteraction.data2,
        },
        {
          name: '建议',
          type: 'bar',
          barWidth: '15%',
          xAxisIndex: 0,
          yAxisIndex: 0,
          itemStyle: {
            // barBorderRadius: [30, 30, 0, 0],
            // color: new echarts.graphic.LinearGradient(
            //   0, 0, 0, 1, [{
            //     offset: 0,
            //     color: '#00feff'
            //   },
            //   {
            //     offset: 0.5,
            //     color: '#027eff'
            //   },
            //   {
            //     offset: 1,
            //     color: '#0286ff'
            //   }
            // ]
            // )
            color: '#30dbad'
          },
          data: props.dataInteraction.data3,
        },
        {
          name: '表扬',
          type: 'bar',
          barWidth: '15%',
          xAxisIndex: 0,
          yAxisIndex: 0,
          itemStyle: {
            // barBorderRadius: [30, 30, 0, 0],
            // color: new echarts.graphic.LinearGradient(
            //   0, 0, 0, 1, [{
            //     offset: 0,
            //     color: '#00feff'
            //   },
            //   {
            //     offset: 0.5,
            //     color: '#027eff'
            //   },
            //   {
            //     offset: 1,
            //     color: '#0286ff'
            //   }
            // ]
            // )
            color: '#d777f4'
          },
          data: props.dataInteraction.data4,
        },

      ]
    };
  option && myEcharts.setOption(option);

}
</script>

<style lang="scss" scoped>
#age {
  width: 500px;
  height: 250px;
  margin: 1% 0;
}

.subtext {
  text-align: center;
  height: 20px;
  width: 100%;
  position: relative;
  top: -220px;
  // right: 10px;

  .name {
    font-size: 12px;
    color: #EDF2FA;
  }

  .n1 {
    color: #0FA3F1;
    font-weight: bold;
    font-size: 16px;
  }

  .n2 {
    color: #F4983C;
    font-weight: bold;
    font-size: 16px;
  }

  .n3 {
    color: #30DBAD;
    font-weight: bold;
    font-size: 16px;
  }

  .n4 {
    color: #D777F4;
    font-weight: bold;
    font-size: 16px;
  }

  .n5 {
    color: #F1728C;
    font-weight: bold;
    font-size: 16px;
  }


}
</style>