<template>
  <div ref="EcharRef2" id="echarts2"></div>
</template>

<script setup>
import * as echarts from "echarts";
import { onMounted, getCurrentInstance, reactive, ref } from 'vue';
// 声明组件要触发的事件
const emits = defineEmits(['increase']);
// 声明接收参数
const props = defineProps({
  dataArray: Array,
  dataArr: Array
});
// //  点击事件
// const handelClick = () => {
//   console.log('触发子组件点击')
//   // 触发父组件事件
//   emits('increase')
// };
onMounted(() => {
  // 获取页面的实例对象
  const pageInstance = getCurrentInstance();
  // 获取dom节点对象
  const tagDomObj = pageInstance.refs.EcharRef2;
  const myEcharts = echarts.init(tagDomObj);
  var spNum = 5, _max = 100;

  var y_data = ['党员数', '公务员数', '退休职工数', '服役军人数', '退役军人数', '困难户数', '残疾人数'].reverse();
  var
    _data1 = [10, 15, 10, 13, 15, 11, 8],
    _data2 = [19, 5, 40, 33, 15, 51, 5];
  var total1 = _data1.reduce((pre, v) => {
    return pre + v
  }, 0)
  var total2 = _data1.reduce((pre, v) => {
    return pre + v
  }, 0)
  var total = total1 + total2
  var s = y_data.map((v, i) => {
    return v + '  ' + (_data1[i] + _data2[i])
  })
  var s1 = _data1.map((v, i) => {
    return (((v + _data2[i]) / total) * 100).toFixed(2);
  })
  var option = {
    backgroundColor: 'transparent',
    grid: {
      containLabel: true,
      top: 20,
      left: -50,
      right: 15,
    },
    tooltip: {
      show: true,
      backgroundColor: '#041A52',
      borderColor: '#65C1DC',
      borderWidth: 1,
      textStyle: {
        color: '#fff',
        fontSize: 16
      },
      formatter: function (p) {
        return `<span>${p.seriesName}：</span><span style="color:yellow;">${p.value}</span>`;
      },
      extraCssText: 'box-shadow: 0 0 5px rgba(0, 0, 0, 0.1)'
    },
    xAxis: {
      splitNumber: spNum,
      interval: _max / spNum,
      max: _max,
      axisLabel: {
        show: false,
        formatter: function (v) {
          var _v = (v / _max * 100).toFixed(0);
          return _v == 0 ? _v : _v + '%';
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
    yAxis: [{
      data: s,
      axisLabel: {
        verticalAlign: 'bottom',
        align: 'left',
        padding: [0, 0, 15, 15],
        textStyle: {
          color: '#fff',
          fontSize: '16',
        },
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      }
    }, {
      triggerEvent: true,
      show: true,
      inverse: true,
      data: s1,
      axisLine: {
        show: false
      },
      splitLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      // 右侧的数值
      axisLabel: {
        interval: 0,
        color: '#5BD7E4',
        align: 'right',
        verticalAlign: 'bottom',
        lineHeight: 30,
        fontSize: 22,
        margin: -20,
        fontStyle: 'italic',
        formatter: function (value, index) {
          return value + '%'
        },
      }
    }],
    series: [{
      type: 'bar',
      name: '男',
      stack: '2',
      // label: _label,
      legendHoverLink: false,
      showBackground: 'true',
      backgroundStyle: {
        borderRadius: 20
      },
      barWidth: 20,
      itemStyle: {
        color: '#7E47FF',
        barBorderRadius: [10, 0, 0, 10],
        emphasis: {
          color: '#7E47FF'
        }
      },
      data: _data1
    }, {
      type: 'bar',
      name: '女',
      stack: '2',
      legendHoverLink: false,
      barWidth: 20,
      // label: _label,
      showBackground: 'true',
      backgroundStyle: {
        borderRadius: 20
      },
      itemStyle: {
        color: '#FD5916',
        barBorderRadius: [0, 10, 10, 0],
        emphasis: {
          color: '#FD5916'
        }
      },
      data: _data2
    }]
  };
  option && myEcharts.setOption(option);
})


</script>

<style lang="scss" scoped>
#echarts2 {
  width: 540px;
  height: 420px;
  margin: 10px -60px;
  background-color: transparent;
}
</style>