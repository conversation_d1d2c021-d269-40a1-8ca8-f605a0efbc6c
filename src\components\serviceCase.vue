<template>
  <div class="_box">
    <div class="single" v-for="item in props.dataService">
      <div class="top">
        <span class="text">{{ item.sum }} <span style="font-size:16px">次/年</span> </span>
        <div style="height:10px"></div>
        <span class="text2">{{ item.name }}（次）</span>
      </div>
      <img src="/di03.png" alt="">
    </div>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue';
const emits = defineEmits(['increase']);
// 声明接收参数
const props = defineProps({
  dataService: Array
});

onMounted(() => {

})
</script>

<style lang="scss" scoped>
._box {
  display: flex;
  justify-content: space-around;
  width: 100%;
  height: 230px;
  flex-wrap: wrap;
  position: relative;
  top: -20px;


  .single {
    color: #fff;
    width: 27%;
    text-align: center;
    margin: -10px 10px;

    img {
      width: 140px;
    }


    .top {
      font-size: 14px;
      position: relative;
      top: 50px;
      font-weight: bold;



      .text {
        font-size: 24px;
        font-weight: bold;
        background-image: -webkit-linear-gradient(bottom, white, #1B7EF2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        position: relative;
        top: 10px;
      }

      .text2 {
        position: relative;
        top: 18px;
      }
    }
  }
}
</style>