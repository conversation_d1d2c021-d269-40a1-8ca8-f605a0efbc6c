<template>
  <div class="dialog">
    <div class="title">
      <span>特殊人群({{ data.tableData1.length + data.tableData2.length + kunjing + data.tableData4.length + data.tableData5.length + jingshen }}人)</span>
    </div>
    <div class="tabs">
      <div class="tab">
        <div class="single" @click="changeIs(1)">
          <div :style="is == 1 ? { 'font-weight': 'bold' } : {}">低保户({{ data.tableData1.length }}人)</div>
          <img src="/selected.png" alt="" v-show="is == 1" />
        </div>
        <div class="single" @click="changeIs(2)">
          <div :style="is == 2 ? { 'font-weight': 'bold' } : {}">失独家庭({{ data.tableData2.length }}人)</div>
          <img src="/selected.png" alt="" v-show="is == 2" />
        </div>
        <div class="single" @click="changeIs(3)">
          <div :style="is == 3 ? { 'font-weight': 'bold' } : {}">困境儿童({{ kunjing }}人)</div>
          <img src="/selected.png" alt="" v-show="is == 3" />
        </div>
        <div class="single" @click="changeIs(4)">
          <div :style="is == 4 ? { 'font-weight': 'bold' } : {}">孤寡老人({{ data.tableData4.length }}人)</div>
          <img src="/selected.png" alt="" v-show="is == 4" />
        </div>
        <div class="single" @click="changeIs(5)">
          <div :style="is == 5 ? { 'font-weight': 'bold' } : {}">残疾人({{ data.tableData5.length }}人)</div>
          <img src="/selected.png" alt="" v-show="is == 5" />
        </div>
        <div class="single" @click="changeIs(6)">
          <div :style="is == 6 ? { 'font-weight': 'bold' } : {}">精神病患者({{ jingshen }}人)</div>
          <img src="/selected.png" alt="" v-show="is == 6" />
        </div>
      </div>
      <div class="close" @click="handelClick">
        <img src="/goback.png" alt="" />
        <span> 返回</span>
      </div>
    </div>
    <table border="0">
      <thead>
        <th>序号</th>
        <th @click="searchName">姓名<img src="../assets/search.png" class="searchs" /></th>
        <th>身份证</th>
        <th>性别</th>
        <th>电话</th>
        <th>小区</th>
        <th>住址</th>
        <th>网格</th>
        <th v-if="is == 5">残疾等级</th>
      </thead>
      <tbody class="tb1" v-if="tableData.length">
        <tr v-for="(v, index) in tableData" :key="v.id">
          <td>{{ index + 1 }}</td>
          <td>{{ v.name }}</td>
          <td>{{ v.id_card }}</td>
          <td>{{ v.gender }}</td>
          <td>{{ v.phone }}</td>
          <td>{{ v.address }}</td>
          <td>{{ v.room }}</td>
          <td>{{ v.grid_name }}</td>
          <td v-if="is == 5">{{ v.special_group }}</td>
        </tr>
      </tbody>
      <tbody v-else>
        <p class="zan">暂无数据！</p>
      </tbody>
    </table>
    <div class="pagination-block">
      <el-pagination layout="prev, pager, next" :total="total" style="--el-pagination-font-size: 16px; --el-pagination-button-disabled-bg-color: none; --el-pagination-bg-color: none; --el-pagination-button-color: white" :current-page="currentPage" :page-size="pageSize" @current-change="handleCurrentChange" />
    </div>
    <div class="searchBox" v-show="showSearch">
      <div class="ipt">姓名 <input type="text" v-model="userName" placeholder="请输入姓名" class="ipt1" /></div>
      <div class="btns">
        <div @click="searchData">搜索</div>
        <div @click="showSearch = !showSearch">取消</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { specialUserData, getSpecialPerson } from '../api'
import { ref, onMounted, reactive } from 'vue'
import { useRoute } from 'vue-router'

const emits = defineEmits(['close'])
// 声明接收参数
const props = defineProps({
  type: String,
  code: String,
})
// //  点击事件
const handelClick = () => {
  console.log('触发子组件点击')
  // 触发父组件事件
  emits('close')
}
let is = ref(1)
let showSearch = ref(false)
let userName = ref('')
const route = useRoute()
let special_type = ref(0)
let tableData = ref([])
let jingshen = ref(0)
let kunjing = ref(0)

let total = ref(1)
let currentPage = ref(1)
let pageSize = ref(15)

const data = reactive({
  tableData: [],
  tableData1: [
    {
      name: '张三',
      sex: '男',
      age: 34,
      carrer: '教师',
      address: '则天一小区',
      phone: 13223566209,
      note: '无',
    },
  ],
  tableData2: [
    {
      name: '李四',
      sex: '男',
      age: 34,
      carrer: '教师',
      address: '则天一小区',
      phone: 13223566209,
      note: '无',
    },
  ],
  tableData3: [
    {
      name: '王五',
      sex: '男',
      age: 34,
      carrer: '教师',
      address: '则天一小区',
      phone: 13223566209,
      note: '无',
    },
  ],
  tableData4: [
    {
      name: '赵六',
      sex: '男',
      age: 34,
      carrer: '教师',
      address: '则天一小区',
      phone: 13223566209,
      note: '无',
    },
  ],
  tableData5: [
    {
      name: '孙七',
      sex: '男',
      age: 34,
      carrer: '教师',
      address: '则天一小区',
      phone: 13223566209,
      note: '无',
    },
  ],
  tableData6: [],
})
function changeIs(val) {
  is.value = val
  special_type.value = val
  if (val == 1) {
    data.tableData = data.tableData1
  } else if (val == 2) {
    data.tableData = data.tableData2
  } else if (val == 3) {
    data.tableData = data.tableData3
  } else if (val == 4) {
    data.tableData = data.tableData4
  } else if (val == 5) {
    data.tableData = data.tableData5
  } else {
    data.tableData = data.tableData6
  }
  getSpecialPersons()
}

function searchName() {
  showSearch.value = true
}
// 搜索
function searchData() {
  // console.log(userName.value, userType.value)
  currentPage.value = 1
  setTimeout(() => {
    getSpecialPersons()
    searchCancel()
  }, 300)
}
function searchCancel() {
  showSearch.value = false
  // userType.value = '全部'
  userName.value = ''
}
// 分页
function handleCurrentChange(v) {
  currentPage.value = v
  getSpecialPersons()
}
function getSpecialPersons() {
  getSpecialPerson({
    limit: 15,
    offset: (currentPage.value / 1 - 1) * 15,
    type: route.query.code ? (route.name == 'a' ? '2' : '3') : '1',
    code: route.query.code,
    name: userName.value,
    special_type: special_type.value ? special_type.value : '',
  }).then((res) => {
    total.value = res.data.count
    tableData.value = res.data.rows
    if (is.value == 3) {
      kunjing.value = res.data.count
    }
  })
}
onMounted(() => {
  getSpecial()
  changeIs(1)
  getSpecialPerson({
    limit: 15,
    offset: (currentPage.value / 1 - 1) * 15,
    type: route.query.code ? (route.name == 'a' ? '2' : '3') : '1',
    code: route.query.code,
    name: userName.value,
    special_type: 6,
  }).then((res) => {
    jingshen.value = res.data.count
  })
})
function getSpecial() {
  specialUserData({
    type: route.query.code ? (route.name == 'a' ? '2' : '3') : '1',
    code: route.query.code,
  }).then((res) => {
    // console.log(res);
    data.tableData1 = res.data.diBao ? res.data.diBao : []
    data.tableData2 = res.data.shiDu ? res.data.shiDu : []
    data.tableData3 = res.data.kunJing ? res.data.kunJing : []
    data.tableData4 = res.data.guGua ? res.data.guGua : []
    data.tableData5 = res.data.canJi ? res.data.canJi : []
  })
}
</script>

<style scoped lang="scss">
.zan {
  position: absolute;
  font-size: 16px;
  color: white;
  top: 30%;
  width: 100%;
  text-align: center;
}

.dialog {
  width: 100%;
  height: 580px;
  background-color: #07192a;
  position: relative;

  .title {
    width: 100%;
    text-align: center;
    height: 38px;
    outline: 1px solid #0c3f7e;
    background: url(/title.png) no-repeat;
    background-position: 50%;
    font-size: 20px;
    font-weight: bold;
    color: #4a97f1;

    span {
      vertical-align: middle;
    }
  }

  .tabs {
    height: 53px;
    position: relative;
    outline: 1px solid #0c3f7e;

    .tab {
      width: 1000px;
      text-align: center;
      font-size: 18px;
      // font-weight: bold;
      color: #ffffff;
      display: flex;

      .single {
        width: 180px;
        height: 35px;
        margin-top: 10px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        cursor: pointer;
      }
    }

    .close {
      position: absolute;
      color: #4a97f1;
      cursor: pointer;
      right: 19px;
      top: 35%;
      font-size: 16px;
    }
  }

  // 表格
  table {
    width: 98%;
    height: 80%;
    margin: 10px auto;
    border: 2px solid #0c3f7e;
    border-collapse: collapse;
  }

  table thead {
    background-color: #0a2846;
    height: 34px;

    th {
      font-size: 18px;
      color: #4a97f1;
    }

    th:nth-child(1) {
      width: 50px;
    }

    th:nth-child(2) {
      width: 100px;
    }

    th:nth-child(3) {
      width: 300px;
    }

    th:nth-child(4) {
      width: 50px;
    }

    th:nth-child(5) {
      width: 150px;
    }

    th:nth-child(7) {
      width: 150px;
    }

    th:nth-child(9) {
      width: 200px;
    }
  }

  table tbody {
    max-height: 570px;
    display: block;
    overflow-y: auto;

    tr:nth-child(even) {
      background: #0e2947;
      height: 38px;

      td {
        text-align: center;
        color: white;
        font-size: 16px;
      }

      td:nth-child(1) {
        width: 50px;
      }

      td:nth-child(2) {
        width: 100px;
      }

      td:nth-child(3) {
        width: 300px;
      }

      td:nth-child(4) {
        width: 50px;
      }

      td:nth-child(5) {
        width: 150px;
      }

      td:nth-child(7) {
        width: 150px;
      }

      td:nth-child(9) {
        width: 200px;
      }
    }

    tr:nth-child(odd) {
      height: 38px;

      td {
        text-align: center;
        color: white;
        font-size: 16px;
      }

      td:nth-child(1) {
        width: 50px;
      }

      td:nth-child(2) {
        width: 100px;
      }

      td:nth-child(3) {
        width: 300px;
      }

      td:nth-child(4) {
        width: 50px;
      }

      td:nth-child(5) {
        width: 150px;
      }

      td:nth-child(7) {
        width: 150px;
      }

      td:nth-child(9) {
        width: 200px;
      }
    }
  }

  table thead,
  table tbody tr {
    width: 100%;
    display: table;
    table-layout: fixed;
  }

  table thead {
    width: calc(100% - 3px);
  }

  table .tb1::-webkit-scrollbar {
    width: 10px !important;
  }

  ::-webkit-scrollbar {
    width: 10px !important;
    height: 16px !important;
    background-color: rgba(21, 40, 79, 0.26);
    border-radius: 10px;
  }

  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    background-color: rgba(21, 40, 79, 0.26);
  }

  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: #234674;
  }
}
.searchs {
  width: 20px;
}
.searchBox {
  width: 500px;
  height: 140px;
  background-color: black;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 30%;
  font-size: 20px;
  border-radius: 20px;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  color: white;
  .label {
    display: inline-block;
    width: 100px;
    text-align: center;
  }

  .btns {
    display: flex;
    div {
      width: 100px;
      text-align: center;
      background-color: #4a97f1;
      height: 50px;
      line-height: 50px;
      border-radius: 15px;
      margin: 0 20px;
      cursor: pointer;
    }
  }
}
.ipt1 {
  width: 200px;
  height: 40px;
  background: transparent;
  border: 2px solid white;
  font-size: 18px;
  color: white;
  text-align: center;
}
.pagination-block {
  position: absolute;
  right: 0;
  bottom: 2.5px;
}
</style>
